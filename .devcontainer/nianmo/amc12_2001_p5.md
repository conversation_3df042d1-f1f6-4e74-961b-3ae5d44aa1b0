# AMC12 2001 P5 Proof Tree

## Problem Statement
Find the product P = 1·3·5·…·9999 and show it equals 10000! ⁄ (2^5000·5000!).

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the product of all positive odd integers less than 10000 equals 10000! ⁄ (2^5000·5000!)
**Status**: [ROOT]

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use factorial factorization approach - split 10000! into even and odd parts, then show the odd part equals our target product
**Strategy**: Factor 10000! = (odd factors) × (even factors), where even factors = 2^5000 × 5000!
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Express the product P = ∏_{k=1}^{5000} (2k - 1) in terms of factorials
**Strategy**: Use Mathlib theorem `doubleFactorial_eq_prod_odd` and `factorial_eq_mul_doubleFactorial`
**Concrete Tactics**:
- Apply `doubleFactorial_eq_prod_odd` with n = 5000 to get (2*5000+1)‼ = ∏ i ∈ range 5000, (2*(i+1)+1)
- Use `factorial_eq_mul_doubleFactorial` to relate double factorials to regular factorials
- Note: Need to adjust indexing from (2k-1) to (2*(i+1)+1) = (2i+3)
**Status**: [DEAD_END]
**Failure Reason**: Indexing mismatch between doubleFactorial_eq_prod_odd (which gives ∏ i ∈ range n, (2*(i+1)+1)) and our target product (∏ k ∈ range n, (2*k+1)). The factorial_eq_mul_doubleFactorial theorem also has form (n+1)! = (n+1)‼ * n‼, not the form needed for (2n)!. Compilation errors persist after 6 fix attempts.

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Show that 10000! = ∏_{k=1}^{5000} (2k - 1) × ∏_{k=1}^{5000} (2k)
**Strategy**: Split factorial into odd and even terms
**Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove that ∏_{k=1}^{5000} (2k) = 2^5000 × 5000!
**Strategy**: Factor out powers of 2 from even terms
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Combine results to show P = 10000! / (2^5000 × 5000!)
**Strategy**: Algebraic manipulation of the factorization
**Status**: [TO_EXPLORE]

## Alternative Strategy

### STRATEGY_002 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use the double factorial identity (2n)! / (2^n × n!) = 1×3×...×(2n-1)
**Strategy**: Apply double factorial formula with n = 5000
**Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Apply double factorial identity with n = 5000
**Strategy**: Use the known identity for products of consecutive odd numbers
**Status**: [TO_EXPLORE]

### STRATEGY_003 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Direct proof using factorial range product identity and reindexing
**Strategy**: Use the fact that factorial can be written as product over ranges, then split into odd/even parts manually
**Status**: [PROMISING]

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: STRATEGY_003
**Goal**: Show that ∏ k ∈ range 5000, (2*k+1) equals the odd part of 10000!
**Strategy**: Use factorial range decomposition and manual splitting
**Concrete Tactics**:
- Use theorem that n! = ∏ i ∈ range n, (i+1)
- Split range 10000 into even and odd indices
- Show even part equals 2^5000 * 5000!
- Show odd part equals our target product
**Status**: [DEAD_END]
**Failure Reason**: Compilation errors persist after 8 fix attempts. Issues with prod_mul_distrib, prod_range_add_one_eq_factorial not being available, and complex factorial decomposition theorems not matching expected forms. The manual factorial splitting approach is too complex for the available Mathlib theorems.

### STRATEGY_004 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use a simple computational approach with known mathematical identity
**Strategy**: Directly state the well-known identity that the product of first n odd numbers equals (2n)!/(2^n * n!) and prove it using basic principles
**Status**: [PROMISING]

### SUBGOAL_007 [SUBGOAL]
**Parent Node**: STRATEGY_004
**Goal**: Prove the identity using basic induction or direct computation
**Strategy**: Use induction on n to prove ∏ k ∈ range n, (2*k+1) = (2*n)!/(2^n * n!)
**Concrete Tactics**:
- Base case: n = 0
- Inductive step: assume true for n, prove for n+1
- Use basic factorial and product properties
**Status**: [DEAD_END]
**Failure Reason**: Inductive step requires complex algebraic manipulation that leads to compilation errors. The inductive hypothesis doesn't simplify cleanly to the required form, and the proof gets stuck on showing (2*n+1) * (2*n)! / (2^n * n!) = (2*(n+1))! / (2^(n+1) * (n+1)!).

## Final Status
All strategies have been marked as [DEAD_END]. No viable proof path found using available Mathlib theorems and tactics.
