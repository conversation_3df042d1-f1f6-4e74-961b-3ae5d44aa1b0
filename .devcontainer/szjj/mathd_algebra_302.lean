import Mathlib.Data.Complex.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.NormNum

-- MATHD Algebra 302: Compute the value of (i/2)^2

theorem mathd_algebra_302 : (Complex.I / 2) ^ 2 = -1 / 4 := by
  -- Step 1: Apply fraction squaring rule: (i/2)^2 = i^2/2^2
  have step1 : (Complex.I / 2) ^ 2 = Complex.I ^ 2 / 2 ^ 2 := by
    rw [div_pow]

  -- Step 2: Apply fundamental property: i^2 = -1
  have step2 : Complex.I ^ 2 = -1 := by
    sorry

  -- Step 3: Compute 2^2 = 4
  have step3 : (2 : ℂ) ^ 2 = 4 := by
    sorry

  -- Step 4: Combine steps to get final result
  rw [step1, step2, step3]
  -- Simplify -1/4 to final form
  sorry
