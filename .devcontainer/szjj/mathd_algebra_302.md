# MATHD Algebra 302 Proof Tree

## Problem Statement
Compute the value of $\left(\dfrac{i}{2}\right)^2$ where $i$ is the imaginary unit.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that $\left(\dfrac{i}{2}\right)^2 = -\dfrac{1}{4}$
**Strategy**: Direct computation using complex number arithmetic and the fundamental property $i^2 = -1$

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Apply the fraction squaring rule to separate numerator and denominator
2. Use the fundamental property $i^2 = -1$
3. Simplify the resulting fraction
4. Express the final result in standard form
**Strategy**: Direct algebraic manipulation with complex number properties

### SUBGOAL_001 [PROMISING]
**Parent Node**: STRATEGY_001
**Goal**: Apply fraction squaring rule: $\left(\frac{i}{2}\right)^{2} = \frac{i^{2}}{2^{2}}$
**Strategy**: Use the property $(a/b)^n = a^n/b^n$ for complex numbers
**Status**: [PROMISING]
**Detailed Plan**: Use div_pow theorem from Mathlib which states (a / b) ^ n = a ^ n / b ^ n for division in fields. This applies directly to complex numbers since ℂ is a field.

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Apply fundamental property: $\frac{i^{2}}{2^{2}} = \frac{-1}{4}$
**Strategy**: Use the defining property $i^2 = -1$ and compute $2^2 = 4$
**Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Simplify to final form: $\frac{-1}{4} = -\frac{1}{4}$
**Strategy**: Apply standard fraction notation and sign rules
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Establish the conclusion: $\left(\dfrac{i}{2}\right)^{2} = -\dfrac{1}{4}$
**Strategy**: Combine all previous steps into a complete proof
**Status**: [TO_EXPLORE]
